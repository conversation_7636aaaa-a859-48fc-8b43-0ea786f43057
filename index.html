<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - GST Calculator</title>
    <meta name="description" content="Privacy Policy for GST Calculator - A simple and elegant GST calculator for Indian businesses">
    <meta name="keywords" content="Privacy Policy, GST Calculator, Indian GST, Tax Calculator">
    <meta name="author" content="GST Calculator Team">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://vishwamartur.github.io/GST-Cal/">
    <meta property="og:title" content="Privacy Policy - GST Calculator">
    <meta property="og:description" content="Privacy Policy for GST Calculator - Your privacy and data security are our top priorities">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://vishwamartur.github.io/GST-Cal/">
    <meta property="twitter:title" content="Privacy Policy - GST Calculator">
    <meta property="twitter:description" content="Privacy Policy for GST Calculator - Your privacy and data security are our top priorities">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1A237E 0%, #3949AB 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .last-updated {
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-size: 0.9em;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #1A237E;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #E3F2FD;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section h3 {
            color: #333;
            font-size: 1.3em;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }
        
        .section p {
            margin-bottom: 15px;
            color: #555;
        }
        
        .section ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        
        .section li {
            margin-bottom: 8px;
            color: #555;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #E8F5E8 0%, #F0F8F0 100%);
            border-left: 4px solid #4CAF50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #E3F2FD 0%, #F0F8FF 100%);
            border: 2px solid #1A237E;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }
        
        .checkmark {
            color: #4CAF50;
            font-weight: bold;
            font-size: 1.1em;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkmark::before {
            content: "✓";
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            flex-shrink: 0;
        }
        
        .contact-box {
            background: #F5F5F5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .footer {
            background: #F8F9FA;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #E0E0E0;
        }
        
        .footer a {
            color: #1A237E;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .app-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .app-link {
            background: #1A237E;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .app-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(26, 35, 126, 0.3);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .section h2 {
                font-size: 1.5em;
            }
            
            .app-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Privacy Policy</h1>
            <div class="subtitle">GST Calculator</div>
            <div class="last-updated">Last updated: December 17, 2024</div>
        </div>
        
        <div class="content">
            <div class="section">
                <p><strong>This Privacy Policy describes how GST Calculator ("we", "our", or "us") collects, uses, and protects your information when you use our mobile application (the "Service").</strong></p>
            </div>

            <div class="highlight-box">
                <h3>📱 About GST Calculator</h3>
                <p>GST Calculator is a simple and elegant GST (Goods and Services Tax) calculator designed for Indian businesses. The app helps users calculate GST for both inclusive and exclusive amounts with support for standard Indian GST rates.</p>
            </div>

            <div class="section">
                <h2>🔒 Information We Collect</h2>
                
                <h3>Data Storage</h3>
                <ul>
                    <li><strong>All calculations and data are stored locally on your device</strong></li>
                    <li><strong>We do not collect, store, or transmit any personal information to external servers</strong></li>
                    <li><strong>No user accounts or registration required</strong></li>
                </ul>

                <h3>Device Information</h3>
                <ul>
                    <li>We may access basic device information necessary for app functionality</li>
                    <li>This includes device type, operating system version for compatibility purposes</li>
                    <li><strong>No personally identifiable information is collected</strong></li>
                </ul>
            </div>

            <div class="section">
                <h2>🛠 How We Use Information</h2>
                
                <h3>Local Data Processing</h3>
                <ul>
                    <li><strong>All GST calculations are performed locally on your device</strong></li>
                    <li><strong>Calculation history is stored locally for your convenience</strong></li>
                    <li><strong>No data is shared with third parties or external services</strong></li>
                </ul>

                <h3>App Functionality</h3>
                <ul>
                    <li>Device information is used solely to ensure proper app functionality</li>
                    <li><strong>No analytics, tracking, or advertising services are integrated</strong></li>
                </ul>
            </div>

            <div class="section">
                <h2>🔐 Data Security</h2>
                
                <h3>Local Storage</h3>
                <ul>
                    <li><strong>All your data remains on your device at all times</strong></li>
                    <li>We implement appropriate security measures to protect locally stored data</li>
                    <li><strong>You have full control over your data and can clear it at any time</strong></li>
                </ul>

                <h3>No External Transmission</h3>
                <ul>
                    <li><strong>No data is transmitted to external servers or third parties</strong></li>
                    <li><strong>No internet connection required for core app functionality</strong></li>
                    <li><strong>Your privacy is protected by design</strong></li>
                </ul>
            </div>

            <div class="section">
                <h2>👤 Your Rights</h2>
                
                <h3>Data Control</h3>
                <ul>
                    <li><strong>You have complete control over your data</strong></li>
                    <li><strong>You can clear all app data through device settings</strong></li>
                    <li><strong>Uninstalling the app removes all associated data</strong></li>
                </ul>

                <h3>No Third-Party Sharing</h3>
                <ul>
                    <li><strong>We do not share, sell, or rent your information to third parties</strong></li>
                    <li><strong>No advertising networks or analytics services have access to your data</strong></li>
                </ul>
            </div>

            <div class="section">
                <h2>👶 Children's Privacy</h2>
                <p>Our Service is suitable for all ages and <strong>does not knowingly collect any personal information from children under 13 years of age</strong>.</p>
            </div>

            <div class="section">
                <h2>📝 Changes to This Privacy Policy</h2>
                <p>We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy in the app and updating the "Last updated" date.</p>
            </div>

            <div class="section">
                <h2>📞 Contact Us</h2>
                <div class="contact-box">
                    <p>If you have any questions about this Privacy Policy, please contact us at:</p>
                    <p><strong>Email:</strong> [Your Email Address]</p>
                </div>
            </div>

            <div class="section">
                <h2>⚖️ Compliance</h2>
                <p>This app complies with:</p>
                <ul>
                    <li>✅ Google Play Store policies</li>
                    <li>✅ General Data Protection Regulation (GDPR)</li>
                    <li>✅ California Consumer Privacy Act (CCPA)</li>
                    <li>✅ Children's Online Privacy Protection Act (COPPA)</li>
                </ul>
            </div>

            <div class="summary-box">
                <h2>📋 Summary</h2>
                <p><strong>GST Calculator is designed with privacy in mind:</strong></p>
                <div style="text-align: left; margin: 20px 0;">
                    <div class="checkmark">No personal data collection</div>
                    <div class="checkmark">All data stored locally on your device</div>
                    <div class="checkmark">No internet connection required</div>
                    <div class="checkmark">No third-party data sharing</div>
                    <div class="checkmark">No advertising or tracking</div>
                    <div class="checkmark">Full user control over data</div>
                </div>
                <p><strong>Your privacy and data security are our top priorities.</strong></p>
            </div>

            <div class="section">
                <h2>📱 Download GST Calculator</h2>
                <div class="app-links">
                    <a href="#" class="app-link">📱 Google Play Store</a>
                    <a href="#" class="app-link">🍎 App Store</a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>This privacy policy is hosted on GitHub Pages and automatically updated.</p>
            <p>
                <a href="https://github.com/vishwamartur/GST-Cal">Source Code</a> |
                <a href="README_BACKUP.md">App Features</a> |
                <a href="PRIVACY_POLICY.txt">Text Version</a>
            </p>
        </div>
    </div>
</body>
</html>
