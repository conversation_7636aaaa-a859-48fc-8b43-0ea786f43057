# GST Calculator

A simple and elegant GST (Goods and Services Tax) calculator for Indian businesses.

## Features

- Calculate GST for both inclusive and exclusive amounts
- Support for standard Indian GST rates (5%, 12%, 18%, 28%, 40%)
- Save calculation history
- Share calculation results
- Customize default settings

## Screenshots

(Add screenshots here)

## Installation

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Expo CLI (optional for mobile development)

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/gst-calculator.git
   cd gst-calculator
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

## Usage

### Web Version

1. Build the web version:
   ```
   npm run build:web
   ```

2. Start the local server:
   ```
   npm run serve
   ```

3. Open your browser and navigate to http://localhost:3000

### Mobile App

1. Start the Expo development server:
   ```
   npm run dev
   ```

2. Scan the QR code with the Expo Go app on your mobile device or use an emulator.

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## Technology Stack

- React Native / Expo
- React Navigation
- AsyncStorage for local data persistence
- Reanimated for animations

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Expo](https://expo.dev/) for the amazing development platform
- [React Native](https://reactnative.dev/) for the cross-platform framework
- [Lucide Icons](https://lucide.dev/) for the beautiful icons
# GST-Cal
