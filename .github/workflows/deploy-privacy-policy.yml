name: Deploy Privacy Policy to GitHub Pages

on:
  push:
    branches: [ main, master ]
    paths:
      - 'PRIVACY_POLICY.txt'
      - 'docs/**'
      - '.github/workflows/deploy-privacy-policy.yml'
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Create docs directory
        run: mkdir -p docs

      - name: Convert privacy policy to HTML
        run: |
          cat > docs/index.html << 'EOF'
          <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>GST Calculator - Privacy Policy</title>
              <style>
                  body {
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
                      line-height: 1.6;
                      max-width: 800px;
                      margin: 0 auto;
                      padding: 20px;
                      color: #333;
                      background-color: #f9f9f9;
                  }
                  .container {
                      background: white;
                      padding: 40px;
                      border-radius: 10px;
                      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                  }
                  h1 {
                      color: #1A237E;
                      text-align: center;
                      border-bottom: 3px solid #1A237E;
                      padding-bottom: 10px;
                  }
                  h2 {
                      color: #1A237E;
                      margin-top: 30px;
                  }
                  .highlight {
                      background-color: #e8f5e8;
                      padding: 15px;
                      border-left: 4px solid #4CAF50;
                      margin: 20px 0;
                  }
                  .summary {
                      background-color: #f0f8ff;
                      padding: 20px;
                      border-radius: 8px;
                      border: 1px solid #1A237E;
                  }
                  .checkmark {
                      color: #4CAF50;
                      font-weight: bold;
                  }
                  .footer {
                      text-align: center;
                      margin-top: 40px;
                      padding-top: 20px;
                      border-top: 1px solid #ddd;
                      color: #666;
                  }
              </style>
          </head>
          <body>
              <div class="container">
                  <h1>GST Calculator - Privacy Policy</h1>
                  <pre id="privacy-content" style="white-space: pre-wrap; font-family: inherit;"></pre>
                  <div class="footer">
                      <p>This privacy policy is automatically deployed via GitHub Actions</p>
                      <p>Last deployed: $(date)</p>
                  </div>
              </div>
              <script>
                  // Load and format the privacy policy content
                  fetch('./privacy-policy.txt')
                      .then(response => response.text())
                      .then(text => {
                          const content = document.getElementById('privacy-content');
                          // Format the text with basic HTML styling
                          let formatted = text
                              .replace(/^([A-Z\s]+)$/gm, '<h2>$1</h2>')
                              .replace(/^([A-Za-z\s]+:)$/gm, '<h3>$1</h3>')
                              .replace(/^(✓.+)$/gm, '<div class="checkmark">$1</div>')
                              .replace(/\n\n/g, '</p><p>')
                              .replace(/^(.)/gm, '<p>$1')
                              .replace(/(.)\n$/gm, '$1</p>');
                          content.innerHTML = formatted;
                      })
                      .catch(() => {
                          // Fallback: display raw text if fetch fails
                          document.getElementById('privacy-content').textContent = 'Privacy policy content will be loaded here.';
                      });
              </script>
          </body>
          </html>
          EOF

      - name: Copy privacy policy text file
        run: cp PRIVACY_POLICY.txt docs/privacy-policy.txt

      - name: Create simple HTML version
        run: |
          cat > docs/privacy.html << 'EOF'
          <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>GST Calculator - Privacy Policy</title>
              <style>
                  body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
                  h1 { color: #1A237E; text-align: center; }
                  h2 { color: #1A237E; margin-top: 30px; }
                  .summary { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
              </style>
          </head>
          <body>
          EOF
          
          # Convert text to HTML
          sed 's/&/\&amp;/g; s/</\&lt;/g; s/>/\&gt;/g' PRIVACY_POLICY.txt | \
          sed 's/^PRIVACY POLICY FOR GST CALCULATOR/<h1>PRIVACY POLICY FOR GST CALCULATOR<\/h1>/' | \
          sed 's/^[A-Z][A-Z\s]*$/<h2>&<\/h2>/' | \
          sed 's/^✓/<span style="color: green;">✓<\/span>/' | \
          sed 's/$//' | \
          sed 's/^$/<br>/' >> docs/privacy.html
          
          echo '</body></html>' >> docs/privacy.html

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./docs

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
