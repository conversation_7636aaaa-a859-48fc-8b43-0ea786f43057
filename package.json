{"name": "gst-calculator", "main": "expo-router/entry", "version": "1.0.0", "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "serve": "node simple-server.js", "deploy:gh-pages": "./deploy-gh-pages.sh", "deploy:firebase": "firebase deploy", "build:android:preview": "eas build -p android --profile preview", "build:android:prod": "eas build -p android --profile production", "build:android:apk": "eas build -p android --profile production-apk", "build:ios:preview": "eas build -p ios --profile preview", "build:ios:prod": "eas build -p ios --profile production", "build:all:preview": "eas build --platform all --profile preview", "build:all:prod": "eas build --platform all --profile production", "deploy:expo": "expo publish", "submit:android": "eas submit -p android", "submit:ios": "eas submit -p ios", "update": "expo install --fix", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo/cli": "^0.24.14", "@expo/vector-icons": "^14.0.2", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "canvas": "^3.1.0", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "^0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "express": "^5.1.0", "lucide-react-native": "^0.475.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5", "sharp": "^0.34.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "eas-cli": "^16.9.0", "typescript": "^5.3.3"}}