# GitHub Actions Workflow for EAS Build and Deploy
# 
# INSTRUCTIONS:
# 1. Create the directory: .github/workflows/
# 2. Copy this file to: .github/workflows/eas-build.yml
# 3. Add EXPO_TOKEN secret in repository settings
# 
# This file cannot be automatically added due to OAuth permissions

name: EAS Build and Deploy

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - android
          - ios
          - web
      profile:
        description: 'Build profile'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - production

jobs:
  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: npm ci

      - name: Build web
        if: ${{ github.event.inputs.platform == 'web' || github.event.inputs.platform == 'all' || github.event_name != 'workflow_dispatch' }}
        run: npm run build:web

      - name: Deploy to Expo
        if: ${{ github.event.inputs.platform == 'web' || github.event.inputs.platform == 'all' || github.event_name != 'workflow_dispatch' }}
        run: npx expo export --platform web

      - name: Build Android
        if: ${{ github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' }}
        run: |
          PROFILE=${{ github.event.inputs.profile || 'preview' }}
          npx eas build --platform android --profile $PROFILE --non-interactive

      - name: Build iOS
        if: ${{ github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' }}
        run: |
          PROFILE=${{ github.event.inputs.profile || 'preview' }}
          npx eas build --platform ios --profile $PROFILE --non-interactive

      - name: Build All Platforms
        if: ${{ github.event.inputs.platform == 'all' && github.event_name == 'workflow_dispatch' }}
        run: |
          PROFILE=${{ github.event.inputs.profile || 'preview' }}
          npx eas build --platform all --profile $PROFILE --non-interactive

  auto-build-on-push:
    name: Auto Build on Push
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "EXPO_TOKEN not found, skipping auto build"
            exit 0
          fi

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: npm ci

      - name: Build web and deploy
        run: |
          npm run build:web
          npx expo export --platform web

      - name: Build preview for all platforms
        run: npx eas build --platform all --profile preview --non-interactive
